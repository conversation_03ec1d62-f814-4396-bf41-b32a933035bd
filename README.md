# VersaTradez - Egyptian Shipping & Courier Website

A modern, responsive website for a shipping and courier company based in Egypt, featuring real-time package tracking, service information, and contact details.

## Features

### 🚚 Modern Design
- Clean, comfortable, and modern UI/UX
- Responsive design that works on all devices
- Custom SVG graphics and animations
- Bootstrap 5 framework for consistency

### 📦 Package Tracking System
- Real-time shipment tracking
- Interactive tracking timeline
- Status updates with visual indicators
- Sample tracking numbers for testing

### 🎨 Visual Elements
- Custom SVG illustrations
- Smooth animations and transitions
- Modern color scheme optimized for Egyptian market
- Professional typography using Poppins font

### 📱 Responsive Features
- Mobile-first design approach
- Touch-friendly navigation
- Optimized for all screen sizes
- Fast loading performance

## Technologies Used

- **HTML5** - Semantic markup and modern structure
- **CSS3** - Custom styling with CSS variables and animations
- **Bootstrap 5** - Responsive grid system and components
- **JavaScript (ES6+)** - Interactive functionality and tracking system
- **SVG Graphics** - Scalable vector graphics for crisp visuals
- **Font Awesome** - Icon library for consistent iconography

## File Structure

```
versa-tradez/
├── index.html          # Main HTML file
├── styles.css          # Custom CSS styling
├── script.js           # JavaScript functionality
└── README.md           # Project documentation
```

## Sample Tracking Numbers

Use these tracking numbers to test the tracking functionality:

- **VT001234567** - Delivered package (Electronics)
- **VT001234568** - In-transit package (Documents)
- **VT001234569** - Recently picked up package (Clothing)

## Sections Overview

### 1. Navigation
- Fixed navigation bar with smooth scrolling
- Responsive mobile menu
- Company logo with SVG icon

### 2. Hero Section
- Eye-catching headline and call-to-action
- Animated SVG illustration
- Gradient background design

### 3. Services Section
- Three main service offerings
- Custom SVG icons for each service
- Hover effects and animations

### 4. Tracking Section
- Interactive tracking form
- Real-time status display
- Timeline visualization
- Error handling for invalid tracking numbers

### 5. About Section
- Company information and statistics
- Egypt map visualization
- Performance metrics display

### 6. Contact Section
- Contact information cards
- Phone, email, and address details
- Social media links

### 7. Footer
- Copyright information
- Social media icons
- Clean, minimal design

## Customization

### Colors
The website uses CSS custom properties for easy color customization:

```css
:root {
    --primary-color: #2563eb;    /* Main brand color */
    --secondary-color: #10b981;  /* Success/tracking color */
    --accent-color: #f59e0b;     /* Warning/highlight color */
    --text-dark: #1f2937;        /* Primary text */
    --text-light: #6b7280;       /* Secondary text */
}
```

### Company Information
Update the following in `index.html`:
- Company name and logo
- Contact information
- Address details
- Social media links

### Tracking System
The tracking system uses sample data stored in `script.js`. In a production environment, replace the `trackingData` object with API calls to your backend tracking system.

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance Features

- Optimized CSS with minimal redundancy
- Efficient JavaScript with event delegation
- Lazy loading for animations
- Compressed and optimized assets

## Future Enhancements

- Backend API integration for real tracking data
- User account system for shipment management
- Online booking and quote system
- Multi-language support (Arabic/English)
- Payment gateway integration
- SMS/Email notification system

## Installation & Setup

1. Download all files to your web server directory
2. Open `index.html` in a web browser
3. For local development, use a local server:
   ```bash
   python -m http.server 8000
   ```
4. Access the website at `http://localhost:8000`

## License

This project is created for VersaTradez shipping company. All rights reserved.

---

**VersaTradez** - Your trusted partner for shipping across Egypt 🇪🇬
