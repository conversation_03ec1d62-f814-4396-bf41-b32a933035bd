<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VersaTradez - Egyptian Shipping & Courier Services</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#home">
                <svg width="40" height="40" viewBox="0 0 40 40" class="me-2">
                    <circle cx="20" cy="20" r="18" fill="#2563eb" stroke="#1d4ed8" stroke-width="2"/>
                    <path d="M12 20l6 6 12-12" stroke="white" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                VersaTradez
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="#home">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="#services">Services</a></li>
                    <li class="nav-item"><a class="nav-link" href="#tracking">Track Shipment</a></li>
                    <li class="nav-item"><a class="nav-link" href="#about">About</a></li>
                    <li class="nav-item"><a class="nav-link" href="#contact">Contact</a></li>
                    <li class="nav-item"><a class="btn btn-primary ms-2" href="contact-form.html">Get Quote</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6">
                    <h1 class="hero-title">Fast & Reliable Shipping Across Egypt</h1>
                    <p class="hero-subtitle">Your trusted partner for local and nationwide courier services. Track your shipments in real-time with our advanced tracking system.</p>
                    <div class="hero-buttons">
                        <a href="#tracking" class="btn btn-primary btn-lg me-3">Track Shipment</a>
                        <a href="#services" class="btn btn-outline-primary btn-lg">Our Services</a>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-image">
                        <svg viewBox="0 0 500 400" class="w-100">
                            <!-- Delivery truck SVG -->
                            <rect x="50" y="200" width="200" height="80" fill="#2563eb" rx="10"/>
                            <rect x="60" y="210" width="180" height="60" fill="#3b82f6" rx="5"/>
                            <circle cx="100" cy="300" r="25" fill="#1f2937"/>
                            <circle cx="200" cy="300" r="25" fill="#1f2937"/>
                            <circle cx="100" cy="300" r="15" fill="#6b7280"/>
                            <circle cx="200" cy="300" r="15" fill="#6b7280"/>
                            <!-- Packages -->
                            <rect x="300" y="180" width="40" height="40" fill="#fbbf24" rx="5"/>
                            <rect x="350" y="160" width="35" height="35" fill="#f59e0b" rx="5"/>
                            <rect x="320" y="230" width="45" height="30" fill="#d97706" rx="5"/>
                            <!-- Route line -->
                            <path d="M250 240 Q350 200 450 240" stroke="#10b981" stroke-width="3" fill="none" stroke-dasharray="10,5"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="services-section py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">Our Services</h2>
                <p class="section-subtitle">Comprehensive shipping solutions for all your needs</p>
            </div>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <svg width="60" height="60" viewBox="0 0 60 60">
                                <circle cx="30" cy="30" r="25" fill="#3b82f6" opacity="0.1"/>
                                <path d="M20 30l8 8 16-16" stroke="#3b82f6" stroke-width="3" fill="none" stroke-linecap="round"/>
                            </svg>
                        </div>
                        <h4>Express Delivery</h4>
                        <p>Same-day and next-day delivery across major Egyptian cities</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <svg width="60" height="60" viewBox="0 0 60 60">
                                <circle cx="30" cy="30" r="25" fill="#10b981" opacity="0.1"/>
                                <rect x="15" y="20" width="30" height="20" fill="none" stroke="#10b981" stroke-width="2" rx="2"/>
                                <path d="M15 25h30M20 30h5M20 35h10" stroke="#10b981" stroke-width="2"/>
                            </svg>
                        </div>
                        <h4>Package Tracking</h4>
                        <p>Real-time tracking with SMS and email notifications</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <svg width="60" height="60" viewBox="0 0 60 60">
                                <circle cx="30" cy="30" r="25" fill="#f59e0b" opacity="0.1"/>
                                <path d="M20 25h20v15H20z" fill="none" stroke="#f59e0b" stroke-width="2"/>
                                <path d="M25 25v-5a5 5 0 0110 0v5" fill="none" stroke="#f59e0b" stroke-width="2"/>
                            </svg>
                        </div>
                        <h4>Secure Handling</h4>
                        <p>Safe and secure handling of all your valuable packages</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tracking Section -->
    <section id="tracking" class="tracking-section py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="tracking-card">
                        <h2 class="text-center mb-4">Track Your Shipment</h2>
                        <form id="trackingForm" class="tracking-form">
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" id="trackingNumber" placeholder="Enter tracking number" required>
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search me-2"></i>Track
                                </button>
                            </div>
                        </form>
                        <div id="trackingResult" class="tracking-result mt-4" style="display: none;">
                            <!-- Tracking results will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about-section py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h2 class="section-title">About VersaTradez</h2>
                    <p class="mb-4">With over a decade of experience in the Egyptian logistics industry, VersaTradez has become a trusted name in shipping and courier services. We pride ourselves on reliability, speed, and customer satisfaction.</p>
                    <div class="stats-row">
                        <div class="stat-item">
                            <h3>10,000+</h3>
                            <p>Packages Delivered</p>
                        </div>
                        <div class="stat-item">
                            <h3>27</h3>
                            <p>Cities Covered</p>
                        </div>
                        <div class="stat-item">
                            <h3>99.5%</h3>
                            <p>On-Time Delivery</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="about-image">
                        <svg viewBox="0 0 400 300" class="w-100">
                            <!-- Egypt map outline (simplified) -->
                            <path d="M50 50 L350 50 L350 200 L200 250 L50 200 Z" fill="#e5e7eb" stroke="#9ca3af" stroke-width="2"/>
                            <!-- Delivery points -->
                            <circle cx="100" cy="100" r="8" fill="#ef4444"/>
                            <circle cx="200" cy="120" r="8" fill="#ef4444"/>
                            <circle cx="280" cy="150" r="8" fill="#ef4444"/>
                            <circle cx="150" cy="180" r="8" fill="#ef4444"/>
                            <!-- Connection lines -->
                            <path d="M100 100 L200 120 L280 150 L150 180" stroke="#3b82f6" stroke-width="3" fill="none"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">Contact Us</h2>
                <p class="section-subtitle">Get in touch for all your shipping needs</p>
                <a href="contact-form.html" class="btn btn-primary btn-lg">Send us a Message</a>
            </div>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h5>Phone</h5>
                        <p>+20 2 1234 5678<br>+20 10 9876 5432</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h5>Email</h5>
                        <p><EMAIL><br><EMAIL></p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <h5>Address</h5>
                        <p>123 Tahrir Square<br>Cairo, Egypt</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 VersaTradez. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="script.js"></script>
</body>
</html>
