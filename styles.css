/* Modern CSS for VersaTradez Shipping Website */

:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --warm-orange: #ff6b35;
    --warm-gold: #ffd700;
    --egyptian-gold: #d4af37;
    --warm-red: #e74c3c;
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --text-muted: #9ca3af;
    --bg-light: #f8fafc;
    --bg-warm: #fef7f0;
    --bg-white: #ffffff;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-warm: 0 8px 25px -5px rgba(255, 107, 53, 0.15);
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.7;
    color: var(--text-dark);
    background-color: var(--bg-white);
    font-size: 16px;
    letter-spacing: 0.01em;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color) !important;
    display: flex;
    align-items: center;
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--text-dark) !important;
    margin: 0 0.5rem;
    transition: var(--transition);
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 50%;
    background-color: var(--primary-color);
    transition: var(--transition);
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
    width: 100%;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--bg-light) 0%, #e0f2fe 100%);
    padding-top: 100px;
    position: relative;
    overflow: hidden;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-light);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-buttons .btn {
    padding: 12px 30px;
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.hero-image {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Section Styling */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-light);
    margin-bottom: 3rem;
}

/* Services Section */
.services-section {
    background-color: var(--bg-white);
}

.service-card {
    background: var(--bg-white);
    padding: 2.5rem 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    height: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.service-icon {
    margin-bottom: 1.5rem;
}

.service-card h4 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.service-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Tracking Section */
.tracking-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
}

.tracking-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 3rem 2rem;
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.tracking-form .form-control {
    padding: 15px 20px;
    border-radius: var(--border-radius);
    border: none;
    font-size: 1.1rem;
}

.tracking-form .btn {
    padding: 15px 25px;
    border-radius: var(--border-radius);
    font-weight: 600;
}

.tracking-result {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* About Section */
.about-section {
    background-color: var(--bg-light);
}

.stats-row {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-item h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-item p {
    color: var(--text-light);
    font-weight: 500;
}

/* Contact Section */
.contact-section {
    background-color: var(--bg-white);
}

.contact-card {
    background: var(--bg-light);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    transition: var(--transition);
    height: 100%;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 1.5rem;
}

.contact-card h5 {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.contact-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Footer */
.footer {
    background-color: var(--text-dark);
    color: white;
}

.social-links a {
    color: white;
    font-size: 1.25rem;
    margin-left: 1rem;
    transition: var(--transition);
}

.social-links a:hover {
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .stats-row {
        flex-direction: column;
        gap: 1rem;
    }
    
    .hero-buttons {
        text-align: center;
    }
    
    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Tracking Status Styles */
.tracking-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 1rem 0;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
}

.status-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
}

.status-step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 15px;
    right: -50%;
    width: 100%;
    height: 2px;
    background: rgba(255, 255, 255, 0.3);
}

.status-step.active::after {
    background: var(--secondary-color);
}

.status-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
    color: white;
}

.status-step.active .status-icon {
    background: var(--secondary-color);
}

.status-text {
    font-size: 0.875rem;
    text-align: center;
    opacity: 0.7;
}

.status-step.active .status-text {
    opacity: 1;
    font-weight: 600;
}

/* Contact Form Styles */
.contact-form-section {
    background-color: var(--bg-light);
    min-height: 100vh;
}

.contact-form-card {
    background: var(--bg-white);
    padding: 3rem 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.contact-form .form-label {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.contact-form .form-control,
.contact-form .form-select {
    padding: 12px 16px;
    border-radius: var(--border-radius);
    border: 2px solid var(--border-color);
    transition: var(--transition);
    font-size: 1rem;
}

.contact-form .form-control:focus,
.contact-form .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.contact-form textarea.form-control {
    resize: vertical;
    min-height: 120px;
}

.contact-info-card {
    background: var(--bg-white);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    height: 100%;
}

.contact-info-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.contact-info-card .contact-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 1.5rem;
}

.contact-info-card h5 {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.contact-info-card p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

/* Form validation styles */
.form-control.is-invalid,
.form-select.is-invalid {
    border-color: #dc3545;
}

.form-control.is-valid,
.form-select.is-valid {
    border-color: var(--secondary-color);
}

/* Testimonials Section */
.testimonials-section {
    background: linear-gradient(135deg, var(--bg-warm) 0%, var(--bg-light) 100%);
}

.testimonial-card {
    background: var(--bg-white);
    padding: 2.5rem 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    height: 100%;
    position: relative;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-warm);
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: 1rem;
    left: 1.5rem;
    font-size: 4rem;
    color: var(--warm-orange);
    opacity: 0.3;
    font-family: serif;
}

.testimonial-text {
    font-style: italic;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    line-height: 1.6;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--warm-orange), var(--egyptian-gold));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.2rem;
}

.author-info h6 {
    margin: 0;
    color: var(--text-dark);
    font-weight: 600;
}

.author-info small {
    color: var(--text-light);
}

.rating {
    color: var(--warm-gold);
    margin-top: 0.5rem;
}

/* Pricing Section */
.pricing-section {
    background-color: var(--bg-white);
}

.pricing-card {
    background: var(--bg-white);
    padding: 2.5rem 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 2px solid var(--border-color);
    transition: var(--transition);
    height: 100%;
    position: relative;
    overflow: hidden;
}

.pricing-card.featured {
    border-color: var(--warm-orange);
    transform: scale(1.05);
}

.pricing-card.featured::before {
    content: 'Most Popular';
    position: absolute;
    top: 1rem;
    right: -2rem;
    background: var(--warm-orange);
    color: white;
    padding: 0.5rem 3rem;
    font-size: 0.875rem;
    font-weight: 600;
    transform: rotate(45deg);
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-warm);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-5px);
}

.price-header h4 {
    color: var(--text-dark);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.price {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--warm-orange);
    margin-bottom: 0.5rem;
}

.price-unit {
    color: var(--text-light);
    font-size: 1rem;
    font-weight: 400;
}

.price-features {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
}

.price-features li {
    padding: 0.5rem 0;
    color: var(--text-dark);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.price-features li i {
    color: var(--secondary-color);
    width: 16px;
}

/* FAQ Section */
.faq-section {
    background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-warm) 100%);
}

.faq-item {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.faq-question {
    background: none;
    border: none;
    width: 100%;
    padding: 1.5rem 2rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-dark);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.faq-question:hover {
    background: var(--bg-light);
}

.faq-question i {
    color: var(--warm-orange);
    transition: var(--transition);
}

.faq-question.active i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 2rem 1.5rem;
    color: var(--text-light);
    line-height: 1.6;
    display: none;
}

.faq-answer.show {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Why Choose Us Section */
.why-choose-section {
    background-color: var(--bg-white);
}

.feature-card {
    text-align: center;
    padding: 2rem 1.5rem;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--warm-orange), var(--egyptian-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
    box-shadow: var(--shadow-md);
}

.feature-card h5 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Live Chat Widget */
.chat-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.chat-button {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--warm-orange), var(--warm-red));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
    border: none;
}

.chat-button:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-warm);
}

.chat-popup {
    position: absolute;
    bottom: 70px;
    right: 0;
    width: 300px;
    background: var(--bg-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    display: none;
}

.chat-popup.show {
    display: block;
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.chat-header {
    background: linear-gradient(135deg, var(--warm-orange), var(--warm-red));
    color: white;
    padding: 1rem;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-body {
    padding: 1.5rem;
    text-align: center;
}

.chat-body p {
    color: var(--text-light);
    margin-bottom: 1rem;
}

/* Notification styles */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification {
    animation: slideIn 0.3s ease;
}
