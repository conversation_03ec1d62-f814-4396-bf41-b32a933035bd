/* Modern CSS for VersaTradez Shipping Website */

:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --warm-orange: #ff6b35;
    --warm-gold: #ffd700;
    --egyptian-gold: #d4af37;
    --warm-red: #e74c3c;
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --text-muted: #9ca3af;
    --bg-light: #f8fafc;
    --bg-warm: #fef7f0;
    --bg-white: #ffffff;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-warm: 0 8px 25px -5px rgba(255, 107, 53, 0.15);
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.7;
    color: var(--text-dark);
    background-color: var(--bg-white);
    font-size: 16px;
    letter-spacing: 0.01em;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Clean Professional Navigation */
.navbar {
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.navbar.scrolled {
    padding: 0.5rem 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    text-decoration: none;
    transition: var(--transition);
}

.navbar-brand:hover {
    transform: scale(1.02);
}

.navbar-brand svg {
    transition: var(--transition);
}

.navbar-brand:hover svg {
    transform: rotate(5deg);
}

.navbar-nav .nav-link {
    color: var(--text-dark);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    border-radius: 8px;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

.navbar-nav .nav-link.active {
    color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background: var(--primary-color);
    transition: var(--transition);
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 80%;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border-radius: 12px;
    padding: 0.5rem 0;
    background: white;
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    color: var(--text-dark);
    font-weight: 500;
    transition: var(--transition);
    border-radius: 8px;
    margin: 0 0.5rem;
}

.dropdown-item:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(5px);
}

.btn {
    font-weight: 600;
    border-radius: 8px;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--warm-orange));
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    border-color: var(--primary-color);
}

/* Mobile Responsive */
@media (max-width: 991px) {
    .navbar-nav {
        margin-top: 1rem;
        margin-bottom: 1rem;
    }

    .navbar-nav .nav-link {
        padding: 0.75rem 0;
        border-bottom: 1px solid var(--border-color);
    }

    .navbar-nav .nav-link:last-child {
        border-bottom: none;
    }

    .d-flex.align-items-center {
        flex-direction: column;
        gap: 1rem;
        width: 100%;
    }

    .d-flex.align-items-center .btn {
        width: 100%;
    }
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--bg-light) 0%, #e0f2fe 50%, var(--bg-warm) 100%);
    padding-top: 100px;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="heroPattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(37, 99, 235, 0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23heroPattern)"/></svg>');
    opacity: 0.5;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-stats {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-light);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-buttons .btn {
    padding: 12px 30px;
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.hero-image {
    animation: float 6s ease-in-out infinite;
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Enhanced visual effects */
.service-card, .testimonial-card, .pricing-card {
    position: relative;
    overflow: hidden;
}

.service-card::before, .testimonial-card::before, .pricing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.service-card:hover::before, .testimonial-card:hover::before, .pricing-card:hover::before {
    left: 100%;
}

/* About section enhancements */
.about-image svg {
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.1));
}

/* Improved animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.feature-icon {
    animation: pulse 3s ease-in-out infinite;
}

/* Background patterns for sections */
.services-section {
    position: relative;
}

.services-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="servicePattern" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M25 0 L50 25 L25 50 L0 25 Z" fill="rgba(37, 99, 235, 0.02)"/></pattern></defs><rect width="100" height="100" fill="url(%23servicePattern)"/></svg>');
    opacity: 0.5;
}

.services-section .container {
    position: relative;
    z-index: 2;
}

/* Section Styling */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-light);
    margin-bottom: 3rem;
}

/* Services Section */
.services-section {
    background-color: var(--bg-white);
}

.service-card {
    background: var(--bg-white);
    padding: 2.5rem 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    height: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.service-icon {
    margin-bottom: 1.5rem;
}

.service-card h4 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.service-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Tracking Section */
.tracking-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
}

.tracking-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 3rem 2rem;
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.tracking-form .form-control {
    padding: 15px 20px;
    border-radius: var(--border-radius);
    border: none;
    font-size: 1.1rem;
}

.tracking-form .btn {
    padding: 15px 25px;
    border-radius: var(--border-radius);
    font-weight: 600;
}

.tracking-result {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* About Section */
.about-section {
    background-color: var(--bg-light);
}

.stats-row {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-item h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-item p {
    color: var(--text-light);
    font-weight: 500;
}

/* Contact Section */
.contact-section {
    background-color: var(--bg-white);
}

.contact-card {
    background: var(--bg-light);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    transition: var(--transition);
    height: 100%;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 1.5rem;
}

.contact-card h5 {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.contact-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Newsletter Section */
.newsletter-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, var(--warm-orange) 100%);
    position: relative;
    overflow: hidden;
}

.newsletter-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.newsletter-form {
    position: relative;
    z-index: 2;
}

.newsletter-form .form-control {
    border: none;
    padding: 12px 20px;
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.newsletter-form .btn {
    border-radius: 50px;
    padding: 12px 30px;
    font-weight: 600;
    border: none;
    background: var(--egyptian-gold);
    color: var(--text-dark);
}

.newsletter-form .btn:hover {
    background: var(--warm-gold);
    transform: translateY(-2px);
}

/* Modern Footer */
.modern-footer {
    background: linear-gradient(135deg, var(--text-dark) 0%, #0f172a 100%);
    color: white;
    position: relative;
}

.modern-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--warm-orange), transparent);
}

.footer-section {
    height: 100%;
}

.footer-logo {
    display: flex;
    align-items: center;
}

.footer-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    margin-left: 0.5rem;
}

.footer-title {
    color: var(--warm-orange);
    font-weight: 600;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    position: relative;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--egyptian-gold);
}

.footer-text {
    color: #cbd5e1;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: #94a3b8;
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.95rem;
}

.footer-links a:hover {
    color: var(--warm-orange);
    padding-left: 5px;
}

.footer-contact .contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.footer-contact .contact-item i {
    font-size: 1.2rem;
    margin-top: 0.2rem;
    width: 20px;
}

.footer-contact .contact-item strong {
    color: white;
    font-size: 0.9rem;
    display: block;
    margin-bottom: 0.25rem;
}

.footer-contact .contact-item p {
    color: #94a3b8;
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.social-links {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.social-link {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: var(--transition);
    font-size: 1.1rem;
}

.social-link.facebook { background: #1877f2; color: white; }
.social-link.twitter { background: #1da1f2; color: white; }
.social-link.instagram { background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888); color: white; }
.social-link.linkedin { background: #0077b5; color: white; }
.social-link.whatsapp { background: #25d366; color: white; }

.social-link:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.app-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.app-button {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: white;
    text-decoration: none;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.app-button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    color: white;
}

.app-button i {
    font-size: 1.5rem;
}

.app-button div small {
    display: block;
    font-size: 0.75rem;
    opacity: 0.8;
}

.app-button div strong {
    font-size: 0.9rem;
}

.cert-badges .badge {
    font-size: 0.7rem;
    padding: 0.4rem 0.6rem;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.3);
}

.legal-links {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.legal-links a {
    color: #94a3b8;
    text-decoration: none;
    font-size: 0.85rem;
    transition: var(--transition);
}

.legal-links a:hover {
    color: var(--warm-orange);
}

.payment-methods {
    text-align: right;
}

.payment-icons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 1rem;
    flex-wrap: wrap;
}

.payment-icons i {
    font-size: 1.5rem;
    color: #94a3b8;
    transition: var(--transition);
}

.payment-icons i:hover {
    color: var(--warm-orange);
    transform: scale(1.1);
}

.payment-text {
    font-size: 0.85rem;
    color: #94a3b8;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .stats-row {
        flex-direction: column;
        gap: 1rem;
    }
    
    .hero-buttons {
        text-align: center;
    }
    
    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Tracking Status Styles */
.tracking-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 1rem 0;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
}

.status-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
}

.status-step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 15px;
    right: -50%;
    width: 100%;
    height: 2px;
    background: rgba(255, 255, 255, 0.3);
}

.status-step.active::after {
    background: var(--secondary-color);
}

.status-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
    color: white;
}

.status-step.active .status-icon {
    background: var(--secondary-color);
}

.status-text {
    font-size: 0.875rem;
    text-align: center;
    opacity: 0.7;
}

.status-step.active .status-text {
    opacity: 1;
    font-weight: 600;
}

/* Contact Form Styles */
.contact-form-section {
    background-color: var(--bg-light);
    min-height: 100vh;
}

.contact-form-card {
    background: var(--bg-white);
    padding: 3rem 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.contact-form .form-label {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.contact-form .form-control,
.contact-form .form-select {
    padding: 12px 16px;
    border-radius: var(--border-radius);
    border: 2px solid var(--border-color);
    transition: var(--transition);
    font-size: 1rem;
}

.contact-form .form-control:focus,
.contact-form .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.contact-form textarea.form-control {
    resize: vertical;
    min-height: 120px;
}

.contact-info-card {
    background: var(--bg-white);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    height: 100%;
}

.contact-info-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.contact-info-card .contact-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 1.5rem;
}

.contact-info-card h5 {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.contact-info-card p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

/* Form validation styles */
.form-control.is-invalid,
.form-select.is-invalid {
    border-color: #dc3545;
}

.form-control.is-valid,
.form-select.is-valid {
    border-color: var(--secondary-color);
}

/* Testimonials Section */
.testimonials-section {
    background: linear-gradient(135deg, var(--bg-warm) 0%, var(--bg-light) 100%);
}

.testimonial-card {
    background: var(--bg-white);
    padding: 2.5rem 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    height: 100%;
    position: relative;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-warm);
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: 1rem;
    left: 1.5rem;
    font-size: 4rem;
    color: var(--warm-orange);
    opacity: 0.3;
    font-family: serif;
}

.testimonial-text {
    font-style: italic;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    line-height: 1.6;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--warm-orange), var(--egyptian-gold));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.2rem;
}

.author-info h6 {
    margin: 0;
    color: var(--text-dark);
    font-weight: 600;
}

.author-info small {
    color: var(--text-light);
}

.rating {
    color: var(--warm-gold);
    margin-top: 0.5rem;
}

/* Pricing Section */
.pricing-section {
    background-color: var(--bg-white);
}

.pricing-card {
    background: var(--bg-white);
    padding: 2.5rem 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 2px solid var(--border-color);
    transition: var(--transition);
    height: 100%;
    position: relative;
    overflow: hidden;
}

.pricing-card.featured {
    border-color: var(--warm-orange);
    transform: scale(1.05);
}

.pricing-card.featured::before {
    content: 'Most Popular';
    position: absolute;
    top: 1rem;
    right: -2rem;
    background: var(--warm-orange);
    color: white;
    padding: 0.5rem 3rem;
    font-size: 0.875rem;
    font-weight: 600;
    transform: rotate(45deg);
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-warm);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-5px);
}

.price-header h4 {
    color: var(--text-dark);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.price {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--warm-orange);
    margin-bottom: 0.5rem;
}

.price-unit {
    color: var(--text-light);
    font-size: 1rem;
    font-weight: 400;
}

.price-features {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
}

.price-features li {
    padding: 0.5rem 0;
    color: var(--text-dark);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.price-features li i {
    color: var(--secondary-color);
    width: 16px;
}

/* FAQ Section */
.faq-section {
    background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-warm) 100%);
}

.faq-item {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.faq-question {
    background: none;
    border: none;
    width: 100%;
    padding: 1.5rem 2rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-dark);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.faq-question:hover {
    background: var(--bg-light);
}

.faq-question i {
    color: var(--warm-orange);
    transition: var(--transition);
}

.faq-question.active i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 2rem 1.5rem;
    color: var(--text-light);
    line-height: 1.6;
    display: none;
}

.faq-answer.show {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Why Choose Us Section */
.why-choose-section {
    background-color: var(--bg-white);
}

.feature-card {
    text-align: center;
    padding: 2rem 1.5rem;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--warm-orange), var(--egyptian-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
    box-shadow: var(--shadow-md);
}

.feature-card h5 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Live Chat Widget */
.chat-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.chat-button {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--warm-orange), var(--warm-red));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
    border: none;
}

.chat-button:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-warm);
}

.chat-popup {
    position: absolute;
    bottom: 70px;
    right: 0;
    width: 300px;
    background: var(--bg-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    display: none;
}

.chat-popup.show {
    display: block;
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.chat-header {
    background: linear-gradient(135deg, var(--warm-orange), var(--warm-red));
    color: white;
    padding: 1rem;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-body {
    padding: 1.5rem;
    text-align: center;
}

.chat-body p {
    color: var(--text-light);
    margin-bottom: 1rem;
}

/* Page Header Styles */
.page-header {
    background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-warm) 100%);
    padding: 120px 0 80px;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="pagePattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(37, 99, 235, 0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23pagePattern)"/></svg>');
    opacity: 0.5;
}

.page-header .container {
    position: relative;
    z-index: 2;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.breadcrumb-item.active {
    color: var(--text-light);
}

.page-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.page-subtitle {
    font-size: 1.25rem;
    color: var(--text-light);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.page-stats {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.page-stats .stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.page-stats .stat-item h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.page-stats .stat-item p {
    color: var(--text-light);
    font-weight: 500;
    margin: 0;
}

.page-header-image img {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

/* Company Story Styles */
.company-story {
    background-color: var(--bg-white);
}

.company-values {
    margin-top: 2rem;
}

.value-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: var(--bg-light);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.value-item:hover {
    transform: translateX(10px);
    box-shadow: var(--shadow-md);
}

.value-item i {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.8);
}

/* Timeline Styles */
.story-timeline {
    position: relative;
    padding-left: 2rem;
}

.story-timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, var(--primary-color), var(--warm-orange));
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
}

.timeline-year {
    background: linear-gradient(135deg, var(--primary-color), var(--warm-orange));
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.9rem;
    min-width: 60px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.timeline-content {
    flex: 1;
    background: var(--bg-white);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.timeline-content h5 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.timeline-content p {
    color: var(--text-light);
    margin: 0;
}

/* Services Page Styles */
.services-grid {
    background-color: var(--bg-white);
}

.service-detail {
    padding: 3rem 0;
    border-bottom: 1px solid var(--border-color);
}

.service-detail:last-child {
    border-bottom: none;
}

.service-content {
    position: relative;
}

.service-badge {
    display: inline-block;
    background: linear-gradient(135deg, var(--warm-orange), var(--egyptian-gold));
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.service-features {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
}

.service-features li {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
}

.service-features li:last-child {
    border-bottom: none;
}

.service-features i {
    font-size: 1.2rem;
}

.service-pricing {
    background: var(--bg-light);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin: 2rem 0;
    text-align: center;
}

.service-pricing .price {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.service-pricing .price-note {
    color: var(--text-light);
    font-size: 0.9rem;
}

.service-image img {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
}

.service-image img:hover {
    transform: scale(1.05);
}

/* Tracking Page Styles */
.tracking-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, var(--warm-orange) 100%);
    color: white;
    padding: 120px 0 80px;
    position: relative;
    overflow: hidden;
}

.tracking-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="trackingPattern" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23trackingPattern)"/></svg>');
    opacity: 0.3;
}

.tracking-hero .container {
    position: relative;
    z-index: 2;
}

.tracking-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.tracking-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 3rem;
    line-height: 1.6;
}

.tracking-form-container {
    max-width: 600px;
    margin: 0 auto;
}

.enhanced-tracking-form .input-group {
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    background: white;
}

.enhanced-tracking-form .input-group-text {
    background: white;
    border: none;
    color: var(--primary-color);
    font-size: 1.2rem;
    padding: 1rem 1.5rem;
}

.enhanced-tracking-form .form-control {
    border: none;
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
    background: white;
}

.enhanced-tracking-form .form-control:focus {
    box-shadow: none;
    background: white;
}

.enhanced-tracking-form .btn {
    padding: 1rem 2rem;
    font-weight: 600;
    border: none;
    background: linear-gradient(135deg, var(--primary-color), var(--warm-orange));
}

.quick-track-buttons {
    text-align: center;
}

.quick-track-buttons .btn {
    border-radius: 20px;
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
    margin: 0.25rem;
}

/* Tracking Features */
.tracking-features .feature-card {
    background: white;
    padding: 2.5rem 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    height: 100%;
}

.tracking-features .feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.tracking-features .feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--warm-orange));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    color: white;
    font-size: 2rem;
    box-shadow: var(--shadow-md);
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }

    .tracking-title {
        font-size: 2.5rem;
    }

    .page-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .timeline-item {
        flex-direction: column;
        text-align: center;
    }

    .story-timeline {
        padding-left: 0;
    }

    .story-timeline::before {
        display: none;
    }
}

/* Notification styles */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification {
    animation: slideIn 0.3s ease;
}
