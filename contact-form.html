<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - VersaTradez</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <svg width="40" height="40" viewBox="0 0 40 40" class="me-2">
                    <circle cx="20" cy="20" r="18" fill="#2563eb" stroke="#1d4ed8" stroke-width="2"/>
                    <path d="M12 20l6 6 12-12" stroke="white" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                VersaTradez
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="index.html">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="index.html#services">Services</a></li>
                    <li class="nav-item"><a class="nav-link" href="index.html#tracking">Track Shipment</a></li>
                    <li class="nav-item"><a class="nav-link" href="index.html#about">About</a></li>
                    <li class="nav-item"><a class="nav-link active" href="#contact">Contact</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Contact Form Section -->
    <section class="contact-form-section py-5" style="margin-top: 100px;">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="text-center mb-5">
                        <h1 class="section-title">Get Your Free Quote</h1>
                        <p class="section-subtitle">Tell us about your shipping needs and we'll provide a customized solution</p>
                        <div class="row justify-content-center">
                            <div class="col-md-8">
                                <div class="d-flex justify-content-center align-items-center gap-4 flex-wrap mb-4">
                                    <div class="text-center">
                                        <i class="fas fa-clock text-primary fs-4"></i>
                                        <small class="d-block text-muted">Response in 2 hours</small>
                                    </div>
                                    <div class="text-center">
                                        <i class="fas fa-shield-alt text-primary fs-4"></i>
                                        <small class="d-block text-muted">100% Secure</small>
                                    </div>
                                    <div class="text-center">
                                        <i class="fas fa-calculator text-primary fs-4"></i>
                                        <small class="d-block text-muted">Free Quote</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="contact-form-card">
                        <form id="contactForm" class="contact-form">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="firstName" class="form-label">First Name *</label>
                                    <input type="text" class="form-control" id="firstName" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="lastName" class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" id="lastName" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="email" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" placeholder="+20 xxx xxx xxxx">
                                </div>
                                <div class="col-md-6">
                                    <label for="serviceType" class="form-label">Service Type</label>
                                    <select class="form-select" id="serviceType">
                                        <option value="">Select a service</option>
                                        <option value="express">Express Delivery (Same/Next Day)</option>
                                        <option value="standard">Standard Delivery (2-3 Days)</option>
                                        <option value="bulk">Business/Bulk Shipping</option>
                                        <option value="international">International Shipping</option>
                                        <option value="tracking">Package Tracking Support</option>
                                        <option value="other">Other Services</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="packageType" class="form-label">Package Type</label>
                                    <select class="form-select" id="packageType">
                                        <option value="">Select package type</option>
                                        <option value="documents">Documents</option>
                                        <option value="electronics">Electronics</option>
                                        <option value="clothing">Clothing & Textiles</option>
                                        <option value="medical">Medical Supplies</option>
                                        <option value="food">Food Items</option>
                                        <option value="fragile">Fragile Items</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <label for="subject" class="form-label">Subject *</label>
                                    <input type="text" class="form-control" id="subject" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="fromCity" class="form-label">From (City)</label>
                                    <select class="form-select" id="fromCity">
                                        <option value="">Select origin city</option>
                                        <option value="cairo">Cairo</option>
                                        <option value="alexandria">Alexandria</option>
                                        <option value="giza">Giza</option>
                                        <option value="luxor">Luxor</option>
                                        <option value="aswan">Aswan</option>
                                        <option value="hurghada">Hurghada</option>
                                        <option value="sharm">Sharm El Sheikh</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="toCity" class="form-label">To (City)</label>
                                    <select class="form-select" id="toCity">
                                        <option value="">Select destination city</option>
                                        <option value="cairo">Cairo</option>
                                        <option value="alexandria">Alexandria</option>
                                        <option value="giza">Giza</option>
                                        <option value="luxor">Luxor</option>
                                        <option value="aswan">Aswan</option>
                                        <option value="hurghada">Hurghada</option>
                                        <option value="sharm">Sharm El Sheikh</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <label for="message" class="form-label">Message *</label>
                                    <textarea class="form-control" id="message" rows="4" required placeholder="Please describe your shipping needs, package details, frequency, and any special requirements..."></textarea>
                                </div>
                                <div class="col-12">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="newsletter">
                                                <label class="form-check-label" for="newsletter">
                                                    Subscribe to shipping updates and promotions
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="urgentQuote">
                                                <label class="form-check-label" for="urgentQuote">
                                                    This is an urgent shipping request
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary btn-lg w-100">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        Send Message
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Contact Information Cards -->
            <div class="row g-4 mt-5">
                <div class="col-md-4">
                    <div class="contact-info-card">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h5>Call Us</h5>
                        <p>+20 2 2574 8900<br>+20 11 2574 8900</p>
                        <small class="text-muted">24/7 Customer Service<br>Arabic & English Support</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="contact-info-card">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h5>Email Us</h5>
                        <p><EMAIL><br><EMAIL></p>
                        <small class="text-muted">Response within 24 hours</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="contact-info-card">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <h5>Visit Us</h5>
                        <p>Building 15, New Cairo<br>First Settlement, Cairo</p>
                        <small class="text-muted">Sun-Thu: 8AM-6PM<br>Fri-Sat: 9AM-3PM</small>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="modern-footer mt-5">
        <div class="container">
            <div class="row g-4 py-4">
                <div class="col-md-4">
                    <div class="footer-section">
                        <div class="footer-logo mb-3">
                            <svg width="30" height="30" viewBox="0 0 40 40" class="me-2">
                                <circle cx="20" cy="20" r="18" fill="#ff6b35" stroke="#d4af37" stroke-width="2"/>
                                <path d="M12 20l6 6 12-12" stroke="white" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span class="footer-brand">VersaTradez</span>
                        </div>
                        <p class="footer-text">Your trusted shipping partner across Egypt</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="footer-section">
                        <h6 class="footer-title">Quick Links</h6>
                        <ul class="footer-links">
                            <li><a href="index.html">Home</a></li>
                            <li><a href="index.html#services">Services</a></li>
                            <li><a href="index.html#tracking">Track Package</a></li>
                            <li><a href="index.html#pricing">Pricing</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="footer-section">
                        <h6 class="footer-title">Contact</h6>
                        <p class="footer-text">+20 2 2574 8900<br><EMAIL></p>
                        <div class="social-links">
                            <a href="#" class="social-link facebook"><i class="fab fa-facebook-f"></i></a>
                            <a href="#" class="social-link twitter"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="social-link instagram"><i class="fab fa-instagram"></i></a>
                            <a href="#" class="social-link whatsapp"><i class="fab fa-whatsapp"></i></a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer-bottom py-3">
                <div class="text-center">
                    <p class="mb-0">&copy; 2024 VersaTradez. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Contact form functionality
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = {
                firstName: document.getElementById('firstName').value,
                lastName: document.getElementById('lastName').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value,
                serviceType: document.getElementById('serviceType').value,
                subject: document.getElementById('subject').value,
                message: document.getElementById('message').value,
                newsletter: document.getElementById('newsletter').checked
            };
            
            // Simulate form submission
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
            submitBtn.disabled = true;
            
            setTimeout(() => {
                // Show success message
                showNotification('Thank you! Your message has been sent successfully. We will get back to you within 24 hours.', 'success');
                
                // Reset form
                this.reset();
                
                // Reset button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 2000);
        });
        
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} notification`;
            notification.style.cssText = `
                position: fixed;
                top: 100px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                animation: slideIn 0.3s ease;
            `;
            notification.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
