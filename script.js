// VersaTradez JavaScript Functionality

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all functionality
    initNavigation();
    initTrackingSystem();
    initAnimations();
    initFormValidation();
    initFAQ();
    initShippingCalculator();
    initChatWidget();
});

// Clean Navigation functionality
function initNavigation() {
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('a[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);

            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80; // Account for navbar
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Navbar scroll effects
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
}

// Tracking system functionality
function initTrackingSystem() {
    const trackingForm = document.getElementById('trackingForm');
    const trackingResult = document.getElementById('trackingResult');
    
    // Sample tracking data (in a real application, this would come from a backend API)
    const trackingData = {
        'VT001234567': {
            status: 'delivered',
            package: 'Samsung Galaxy Phone',
            sender: 'TechZone Cairo',
            recipient: 'Ahmed Mohamed',
            destination: 'Alexandria, Egypt',
            estimatedDelivery: '2024-01-15',
            actualDelivery: '2024-01-15',
            weight: '0.5kg',
            value: '15,000 EGP',
            timeline: [
                { status: 'picked-up', date: '2024-01-13 09:00', location: 'Nasr City, Cairo', description: 'Package picked up from TechZone store' },
                { status: 'in-transit', date: '2024-01-13 14:30', location: 'Cairo Distribution Center', description: 'Package processed and sorted' },
                { status: 'in-transit', date: '2024-01-14 08:00', location: 'Alexandria Hub', description: 'Arrived at Alexandria distribution center' },
                { status: 'out-for-delivery', date: '2024-01-15 09:00', location: 'Alexandria - Sidi Gaber', description: 'Out for delivery with driver Mohamed Ali' },
                { status: 'delivered', date: '2024-01-15 14:30', location: 'Alexandria - Sidi Gaber', description: 'Delivered successfully - Signed by Ahmed Mohamed' }
            ]
        },
        'VT001234568': {
            status: 'out-for-delivery',
            package: 'Legal Documents',
            sender: 'Hassan & Partners Law Firm',
            recipient: 'Fatma Hassan',
            destination: 'Dokki, Giza',
            estimatedDelivery: '2024-01-16',
            weight: '0.2kg',
            value: '500 EGP',
            timeline: [
                { status: 'picked-up', date: '2024-01-14 10:00', location: 'Downtown Cairo', description: 'Documents collected from law office' },
                { status: 'in-transit', date: '2024-01-14 15:00', location: 'Cairo Distribution Center', description: 'Express processing completed' },
                { status: 'in-transit', date: '2024-01-15 08:00', location: 'Giza Hub', description: 'Transferred to Giza delivery team' },
                { status: 'out-for-delivery', date: '2024-01-16 09:30', location: 'Dokki, Giza', description: 'Out for delivery with driver Sara Ahmed' }
            ]
        },
        'VT001234569': {
            status: 'in-transit',
            package: 'Fashion Items',
            sender: 'Alexandria Fashion Boutique',
            recipient: 'Omar Ali',
            destination: 'Luxor, Egypt',
            estimatedDelivery: '2024-01-17',
            weight: '1.2kg',
            value: '2,500 EGP',
            timeline: [
                { status: 'picked-up', date: '2024-01-15 11:00', location: 'Alexandria - Sporting', description: 'Package collected from boutique' },
                { status: 'in-transit', date: '2024-01-15 16:00', location: 'Alexandria Hub', description: 'Processed for long-distance shipping' },
                { status: 'in-transit', date: '2024-01-16 06:00', location: 'Cairo Transit Hub', description: 'In transit to Upper Egypt' }
            ]
        },
        'VT001234570': {
            status: 'delivered',
            package: 'Medical Supplies',
            sender: 'Cairo Medical Center',
            recipient: 'Dr. Mona Farid',
            destination: 'Aswan, Egypt',
            estimatedDelivery: '2024-01-14',
            actualDelivery: '2024-01-14',
            weight: '2.0kg',
            value: '8,000 EGP',
            timeline: [
                { status: 'picked-up', date: '2024-01-12 08:00', location: 'Heliopolis, Cairo', description: 'Medical supplies collected with special handling' },
                { status: 'in-transit', date: '2024-01-12 12:00', location: 'Cairo Distribution Center', description: 'Priority processing for medical shipment' },
                { status: 'in-transit', date: '2024-01-13 18:00', location: 'Luxor Hub', description: 'Transferred to Upper Egypt network' },
                { status: 'out-for-delivery', date: '2024-01-14 07:00', location: 'Aswan', description: 'Out for delivery with specialized courier' },
                { status: 'delivered', date: '2024-01-14 11:30', location: 'Aswan Medical Complex', description: 'Delivered to Dr. Mona Farid - Temperature controlled delivery confirmed' }
            ]
        }
    };

    trackingForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const trackingNumber = document.getElementById('trackingNumber').value.trim().toUpperCase();
        
        if (!trackingNumber) {
            showTrackingError('Please enter a tracking number');
            return;
        }

        // Show loading state
        showTrackingLoading();

        // Simulate API call delay
        setTimeout(() => {
            const shipment = trackingData[trackingNumber];
            
            if (shipment) {
                displayTrackingResult(trackingNumber, shipment);
            } else {
                showTrackingError('Tracking number not found. Please check and try again.');
            }
        }, 1500);
    });
}

function showTrackingLoading() {
    const trackingResult = document.getElementById('trackingResult');
    trackingResult.style.display = 'block';
    trackingResult.innerHTML = `
        <div class="text-center">
            <div class="loading"></div>
            <p class="mt-3">Searching for your package...</p>
        </div>
    `;
}

function showTrackingError(message) {
    const trackingResult = document.getElementById('trackingResult');
    trackingResult.style.display = 'block';
    trackingResult.innerHTML = `
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
        </div>
    `;
}

function displayTrackingResult(trackingNumber, shipment) {
    const trackingResult = document.getElementById('trackingResult');
    
    const statusSteps = [
        { key: 'picked-up', label: 'Picked Up', icon: 'fas fa-box' },
        { key: 'in-transit', label: 'In Transit', icon: 'fas fa-truck' },
        { key: 'out-for-delivery', label: 'Out for Delivery', icon: 'fas fa-shipping-fast' },
        { key: 'delivered', label: 'Delivered', icon: 'fas fa-check-circle' }
    ];

    const currentStatusIndex = statusSteps.findIndex(step => step.key === shipment.status);
    
    let statusHTML = '<div class="tracking-status">';
    statusSteps.forEach((step, index) => {
        const isActive = index <= currentStatusIndex;
        statusHTML += `
            <div class="status-step ${isActive ? 'active' : ''}">
                <div class="status-icon">
                    <i class="${step.icon}"></i>
                </div>
                <div class="status-text">${step.label}</div>
            </div>
        `;
    });
    statusHTML += '</div>';

    let timelineHTML = '<div class="timeline mt-4">';
    shipment.timeline.reverse().forEach(event => {
        timelineHTML += `
            <div class="timeline-item mb-3">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1">${event.description}</h6>
                        <small class="text-muted">${event.location}</small>
                    </div>
                    <small class="text-muted">${formatDate(event.date)}</small>
                </div>
            </div>
        `;
    });
    timelineHTML += '</div>';

    trackingResult.innerHTML = `
        <div class="alert alert-success" role="alert">
            <h5 class="alert-heading">
                <i class="fas fa-package me-2"></i>
                Package Found: ${trackingNumber}
            </h5>
            <hr>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Package:</strong> ${shipment.package}</p>
                    <p><strong>From:</strong> ${shipment.sender}</p>
                    <p><strong>To:</strong> ${shipment.recipient}</p>
                    <p><strong>Weight:</strong> ${shipment.weight || 'N/A'}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>Destination:</strong> ${shipment.destination}</p>
                    <p><strong>Status:</strong> <span class="badge bg-primary">${shipment.status.replace('-', ' ').toUpperCase()}</span></p>
                    <p><strong>Est. Delivery:</strong> ${shipment.estimatedDelivery}</p>
                    <p><strong>Declared Value:</strong> ${shipment.value || 'N/A'}</p>
                </div>
            </div>
        </div>
        
        ${statusHTML}
        
        <h6 class="mt-4 mb-3">Tracking Timeline</h6>
        ${timelineHTML}
    `;
    
    trackingResult.style.display = 'block';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Animation functionality
function initAnimations() {
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe service cards and other elements
    const animatedElements = document.querySelectorAll('.service-card, .contact-card, .stat-item');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Form validation
function initFormValidation() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const inputs = form.querySelectorAll('input[required]');
            let isValid = true;

            inputs.forEach(input => {
                if (!input.value.trim()) {
                    isValid = false;
                    input.classList.add('is-invalid');
                } else {
                    input.classList.remove('is-invalid');
                }
            });

            if (!isValid) {
                e.preventDefault();
            }
        });
    });
}

// Utility functions
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification`;
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideIn 0.3s ease;
    `;
    notification.innerHTML = `
        <i class="fas fa-info-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// FAQ functionality
function initFAQ() {
    // FAQ toggle functionality is handled by inline onclick
}

function toggleFAQ(button) {
    const answer = button.nextElementSibling;
    const icon = button.querySelector('i');

    // Close all other FAQs
    document.querySelectorAll('.faq-question').forEach(q => {
        if (q !== button) {
            q.classList.remove('active');
            q.querySelector('i').style.transform = 'rotate(0deg)';
            q.nextElementSibling.classList.remove('show');
        }
    });

    // Toggle current FAQ
    button.classList.toggle('active');
    answer.classList.toggle('show');

    if (button.classList.contains('active')) {
        icon.style.transform = 'rotate(180deg)';
    } else {
        icon.style.transform = 'rotate(0deg)';
    }
}

// Shipping Calculator
function initShippingCalculator() {
    // Calculator will be implemented as a modal
}

function openShippingCalculator() {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Shipping Calculator</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="shippingCalculatorForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">From (City)</label>
                                <select class="form-select" id="fromCity" required>
                                    <option value="">Select origin city</option>
                                    <option value="cairo">Cairo</option>
                                    <option value="alexandria">Alexandria</option>
                                    <option value="giza">Giza</option>
                                    <option value="luxor">Luxor</option>
                                    <option value="aswan">Aswan</option>
                                    <option value="hurghada">Hurghada</option>
                                    <option value="sharm">Sharm El Sheikh</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">To (City)</label>
                                <select class="form-select" id="toCity" required>
                                    <option value="">Select destination city</option>
                                    <option value="cairo">Cairo</option>
                                    <option value="alexandria">Alexandria</option>
                                    <option value="giza">Giza</option>
                                    <option value="luxor">Luxor</option>
                                    <option value="aswan">Aswan</option>
                                    <option value="hurghada">Hurghada</option>
                                    <option value="sharm">Sharm El Sheikh</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Weight (kg)</label>
                                <input type="number" class="form-control" id="weight" min="0.1" max="50" step="0.1" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Service Type</label>
                                <select class="form-select" id="serviceType" required>
                                    <option value="">Select service</option>
                                    <option value="local">Local Delivery</option>
                                    <option value="express">Express Nationwide</option>
                                    <option value="standard">Standard Delivery</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">Calculate Shipping Cost</button>
                            </div>
                        </div>
                    </form>
                    <div id="calculatorResult" class="mt-4" style="display: none;"></div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Handle form submission
    document.getElementById('shippingCalculatorForm').addEventListener('submit', function(e) {
        e.preventDefault();
        calculateShipping();
    });

    // Clean up when modal is hidden
    modal.addEventListener('hidden.bs.modal', function() {
        modal.remove();
    });
}

function calculateShipping() {
    const fromCity = document.getElementById('fromCity').value;
    const toCity = document.getElementById('toCity').value;
    const weight = parseFloat(document.getElementById('weight').value);
    const serviceType = document.getElementById('serviceType').value;

    // Simple pricing logic
    let basePrice = 25; // Local delivery base price
    let deliveryTime = 'Same day';

    if (fromCity !== toCity) {
        basePrice = 45; // Nationwide base price
        deliveryTime = 'Next day';
    }

    if (serviceType === 'standard') {
        basePrice *= 0.8; // 20% discount for standard
        deliveryTime = fromCity === toCity ? 'Same day' : '2-3 days';
    }

    // Weight multiplier
    if (weight > 5) {
        basePrice += (weight - 5) * 5;
    }

    const total = Math.round(basePrice);

    document.getElementById('calculatorResult').innerHTML = `
        <div class="alert alert-success">
            <h6>Shipping Quote</h6>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Route:</strong> ${fromCity.charAt(0).toUpperCase() + fromCity.slice(1)} → ${toCity.charAt(0).toUpperCase() + toCity.slice(1)}</p>
                    <p><strong>Weight:</strong> ${weight} kg</p>
                </div>
                <div class="col-md-6">
                    <p><strong>Service:</strong> ${serviceType.charAt(0).toUpperCase() + serviceType.slice(1)}</p>
                    <p><strong>Delivery Time:</strong> ${deliveryTime}</p>
                </div>
            </div>
            <hr>
            <h4 class="text-primary">Total Cost: ${total} EGP</h4>
            <small class="text-muted">Price includes insurance up to 1,000 EGP</small>
        </div>
    `;

    document.getElementById('calculatorResult').style.display = 'block';
}

// Chat Widget functionality
function initChatWidget() {
    // Chat widget is initialized with inline onclick handlers
}

function toggleChat() {
    const popup = document.getElementById('chatPopup');
    popup.classList.toggle('show');
}

function startChat(type) {
    let message = '';
    switch(type) {
        case 'pricing':
            message = 'Hi! I need information about shipping rates and pricing.';
            break;
        case 'tracking':
            message = 'Hello! I need help tracking my package.';
            break;
        case 'support':
            message = 'Hi! I need general support with shipping services.';
            break;
    }

    // Simulate opening a chat with the message
    showNotification(`Chat started: "${message}" - A customer service representative will respond shortly.`, 'info');
    toggleChat();
}

// Add CSS for notification animation
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    .notification {
        animation: slideIn 0.3s ease;
    }
`;
document.head.appendChild(style);
